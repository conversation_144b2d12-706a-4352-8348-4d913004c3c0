'use client'

import React from 'react'
import Link from 'next/link'
import {
  AppShell,
  Avatar,
  Box,
  Burger,
  Group,
  Image,
  Menu,
  NavLink,
  ScrollArea,
  Title,
  UnstyledButton,
} from '@mantine/core'
import { useDisclosure } from '@mantine/hooks'
import {
  IconBell,
  IconChevronRight,
  IconContract,
  IconDashboard,
  IconFileText,
  IconLogout,
  IconNews,
  IconSettings,
  IconShield,
  IconTrendingUp,
  IconUser,
} from '@tabler/icons-react'

// Types for navigation structure
interface NavigationChild {
  label: string
  href: string
}

interface NavigationItem {
  label: string
  icon: React.ComponentType<{ size?: number; stroke?: number }>
  href?: string
  children?: NavigationChild[]
}

const user = {
  name: 'System',
  avatar: '/system.svg',
}

// Navigation data structure matching the reference design
const navigationData: NavigationItem[] = [
  {
    label: 'Dashboard',
    icon: IconDashboard,
    href: '/',
  },
  {
    label: 'Market news',
    icon: IconNews,
    children: [
      { label: 'Overview', href: '/market-news/overview' },
      { label: 'Forecasts', href: '/market-news/forecasts' },
      { label: 'Outlook', href: '/market-news/outlook' },
      { label: 'Real time', href: '/market-news/real-time' },
    ],
  },
  {
    label: 'Releases',
    icon: IconFileText,
    children: [
      { label: 'Upcoming releases', href: '/releases/upcoming' },
      { label: 'Previous releases', href: '/releases/previous' },
      { label: 'Releases schedule', href: '/releases/schedule' },
    ],
  },
  {
    label: 'Analytics',
    icon: IconTrendingUp,
    href: '/analytics',
  },
  {
    label: 'Contracts',
    icon: IconContract,
    href: '/contracts',
  },
  {
    label: 'Settings',
    icon: IconSettings,
    children: [{ label: 'Security', href: '/settings/security' }],
  },
]

// Security submenu data
const securitySubmenu = [
  { label: 'Enable 2FA', href: '/settings/security/2fa' },
  { label: 'Change password', href: '/settings/security/password' },
  { label: 'Recovery codes', href: '/settings/security/recovery' },
]

// Helper component for rendering navigation items
function NavigationItemComponent({
  item,
  pathname = '/',
}: {
  item: NavigationItem
  pathname?: string
}) {
  const hasChildren = item.children && item.children.length > 0
  const isActive = pathname === item.href

  if (hasChildren) {
    return (
      <NavLink
        key={item.label}
        label={item.label}
        leftSection={<item.icon size={16} stroke={1.5} />}
        rightSection={<IconChevronRight size={12} stroke={1.5} className="mantine-rotate-rtl" />}
        childrenOffset={28}
        defaultOpened={item.label === 'Market news'} // Market news is open by default in reference
      >
        {item.children?.map((child) => (
          <NavLink
            key={child.label}
            label={child.label}
            href={child.href}
            component={Link}
            active={pathname === child.href}
          />
        ))}
        {/* Special handling for Security submenu */}
        {item.label === 'Settings' && (
          <NavLink
            label="Security"
            leftSection={<IconShield size={16} stroke={1.5} />}
            rightSection={
              <IconChevronRight size={12} stroke={1.5} className="mantine-rotate-rtl" />
            }
            childrenOffset={28}
          >
            {securitySubmenu.map((securityItem) => (
              <NavLink
                key={securityItem.label}
                label={securityItem.label}
                href={securityItem.href}
                component={Link}
                active={pathname === securityItem.href}
              />
            ))}
          </NavLink>
        )}
      </NavLink>
    )
  }

  if (!item.href) {
    return null
  }

  return (
    <NavLink
      key={item.label}
      label={item.label}
      href={item.href}
      component={Link}
      leftSection={<item.icon size={16} stroke={1.5} />}
      active={isActive}
    />
  )
}

export function SidebarLayout() {
  const [opened, { toggle }] = useDisclosure()

  return (
    <AppShell
      header={{ height: '4rem' }}
      navbar={{
        width: '16rem',
        breakpoint: 'sm',
        collapsed: { mobile: !opened },
      }}
      padding="sm"
    >
      <AppShell.Header>
        <Group h="100%" px="md" justify="space-between">
          <Group>
            <Burger opened={opened} onClick={toggle} hiddenFrom="sm" size="sm" />
            <UnstyledButton component={Link} href="/">
              <Group gap="sm">
                <Image src="/logo.svg" alt="Logo" w={32} h={32} />
                <Title size="h4">Mantine</Title>
              </Group>
            </UnstyledButton>
          </Group>
          <Menu shadow="md" width={200} position="bottom-end">
            <Menu.Target>
              <Avatar src={user.avatar} radius="xl">
                {user.name.slice(0, 2).toUpperCase()}
              </Avatar>
            </Menu.Target>
            <Menu.Dropdown>
              <Menu.Label>Account</Menu.Label>
              <Menu.Item leftSection={<IconUser size={16} />}>Profile</Menu.Item>
              <Menu.Item leftSection={<IconSettings size={16} />}>Settings</Menu.Item>
              <Menu.Item leftSection={<IconBell size={16} />}>Notifications</Menu.Item>
              <Menu.Divider />
              <Menu.Item leftSection={<IconLogout size={16} />} color="red">
                Sign out
              </Menu.Item>
            </Menu.Dropdown>
          </Menu>
        </Group>
      </AppShell.Header>

      <AppShell.Navbar py->
        <ScrollArea>
          {navigationData.map((item) => (
            <NavigationItemComponent key={item.label} item={item} />
          ))}
        </ScrollArea>
      </AppShell.Navbar>

      <AppShell.Main>
        <Box>{/* Main content area */}</Box>
      </AppShell.Main>
    </AppShell>
  )
}
